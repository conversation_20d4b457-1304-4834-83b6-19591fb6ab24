import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// Helper function to generate slug from title
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Interface definitions
interface MiniPackageData {
  id?: string;
  title: string;
  slug?: string;
  difficulty: string;
  category: string;
  location: string;
  duration?: string;
  pricing_solo: number;
  pricing_honeymoon: number;
  pricing_family: number;
  pricing_group: number;
  status?: string;

  image_url?: string;
  image_alt?: string;
  hero_image_url?: string;
  hero_image_alt?: string;
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
  og_title?: string;
  og_description?: string;
  og_image_url?: string;
  canonical_url?: string;
  robots_index?: string;
  robots_follow?: string;
  schema_data?: any;
  highlights?: string[];
  packing_list?: string[];
  includes?: string[];
  excludes?: string[];
}

interface ContentBlock {
  id?: string;
  block_type: string;
  content?: string;
  content_data?: any;
  image_url?: string;
  image_alt?: string;
  image_caption?: string;
  sort_order: number;
}

interface ItineraryDay {
  id?: string;
  hour_number: number;
  title: string;
  description: string;
  sort_order: number;
}

interface MiniPackageImage {
  id?: string;
  image_url: string;
  image_alt: string;
  caption?: string;
  sort_order: number;
  is_featured?: boolean;
}

// GET - Fetch mini packages with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const category = searchParams.get('category') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Build query
    let query = supabase
      .from('sas_mini_packages')
      .select(`
        id,
        title,
        slug,
        location,
        pricing_solo,
        pricing_honeymoon,
        pricing_family,
        pricing_group,
        category,
        difficulty,
        duration,
        status,
        image_url,
        image_alt,
        created_at,
        updated_at,
        published_at
      `);

    // Apply filters
    if (search) {
      query = query.or(`title.ilike.%${search}%,location.ilike.%${search}%`);
    }

    if (status !== 'all') {
      query = query.eq('status', status);
    }

    if (category !== 'all') {
      query = query.eq('category', category);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Order by created_at desc
    query = query.order('created_at', { ascending: false });

    const { data: miniPackages, error, count } = await query;

    if (error) {
      console.error('Error fetching mini packages:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch mini packages' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    const { count: totalCount } = await supabase
      .from('sas_mini_packages')
      .select('*', { count: 'exact', head: true });

    return NextResponse.json({
      success: true,
      data: miniPackages,
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error in GET /api/admin/mini-packages:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new mini package
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Extract main mini package data
    const {
      title,
      location,
      difficulty,
      category,
      duration,
      pricing,
      status = 'draft',
      imageUrl,
      imageAlt,
      heroImageUrl,
      heroImageAlt,
      seoTitle,
      seoDescription,
      seoKeywords,
      ogTitle,
      ogDescription,
      ogImageUrl,
      canonicalUrl,
      robotsIndex,
      robotsFollow,
      highlights,
      packingList,
      includes,
      excludes,
      content = [],
      itinerary = [],
      images = []
    } = body;

    // Generate slug from title
    const slug = generateSlug(title);

    // Check if slug already exists
    const { data: existingMiniPackage } = await supabase
      .from('sas_mini_packages')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingMiniPackage) {
      return NextResponse.json(
        { success: false, error: 'A mini package with this title already exists' },
        { status: 400 }
      );
    }

    // Prepare mini package data
    const miniPackageData: MiniPackageData = {
      title,
      slug,
      location,
      difficulty,
      category,
      duration,
      pricing_solo: pricing?.solo || 0,
      pricing_honeymoon: pricing?.honeymoon || 0,
      pricing_family: pricing?.family || 0,
      pricing_group: pricing?.group || 0,
      status,
      image_url: imageUrl,
      image_alt: imageAlt || '',
      hero_image_url: heroImageUrl,
      hero_image_alt: heroImageAlt || '',
      seo_title: seoTitle,
      seo_description: seoDescription,
      seo_keywords: seoKeywords,
      og_title: ogTitle,
      og_description: ogDescription,
      og_image_url: ogImageUrl,
      canonical_url: canonicalUrl,
      robots_index: robotsIndex || 'index',
      robots_follow: robotsFollow || 'follow',
      highlights,
      packing_list: packingList,
      includes,
      excludes
    };

    // Set published_at if status is active
    if (status === 'active') {
      (miniPackageData as any).published_at = new Date().toISOString();
    }

    // Insert mini package
    const { data: newMiniPackage, error: miniPackageError } = await supabase
      .from('sas_mini_packages')
      .insert([miniPackageData])
      .select()
      .single();

    if (miniPackageError) {
      console.error('Error creating mini package:', miniPackageError);
      return NextResponse.json(
        { success: false, error: 'Failed to create mini package' },
        { status: 500 }
      );
    }

    const miniPackageId = newMiniPackage.id;

    // Insert content blocks if provided
    if (content && content.length > 0) {
      const contentBlocks = content.map((block: ContentBlock) => ({
        mini_package_id: miniPackageId,
        block_type: block.block_type,
        content: block.content,
        content_data: block.content_data,
        image_url: block.image_url,
        image_alt: block.image_alt,
        image_caption: block.image_caption,
        sort_order: block.sort_order
      }));

      const { error: contentError } = await supabase
        .from('sas_mini_package_content_blocks')
        .insert(contentBlocks);

      if (contentError) {
        console.error('Error creating content blocks:', contentError);
      }
    }

    // Insert itinerary if provided
    if (itinerary && itinerary.length > 0) {
      const itineraryData = itinerary.map((day: ItineraryDay) => ({
        mini_package_id: miniPackageId,
        hour_number: day.hour_number,
        title: day.title,
        description: day.description,
        sort_order: day.sort_order
      }));

      const { error: itineraryError } = await supabase
        .from('sas_mini_package_itinerary')
        .insert(itineraryData);

      if (itineraryError) {
        console.error('Error creating itinerary:', itineraryError);
      }
    }

    // Insert images if provided
    if (images && images.length > 0) {
      const imageData = images.map((image: MiniPackageImage) => ({
        mini_package_id: miniPackageId,
        image_url: image.image_url,
        image_alt: image.image_alt,
        caption: image.caption,
        sort_order: image.sort_order,
        is_featured: image.is_featured || false
      }));

      const { error: imagesError } = await supabase
        .from('sas_mini_package_images')
        .insert(imageData);

      if (imagesError) {
        console.error('Error creating images:', imagesError);
      }
    }

    return NextResponse.json({
      success: true,
      data: newMiniPackage,
      message: 'Mini package created successfully'
    });

  } catch (error) {
    console.error('Error in POST /api/admin/mini-packages:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
