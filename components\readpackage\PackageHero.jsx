'use client';
'use client';
import React, { useState } from 'react';
import { getTemporaryFallbackImage } from '@/lib/fallback-images';
import LazyImage from '../common/LazyImage';

const PackageHero = ({ image, title }) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Debug: Log the image URL
  console.log('PackageHero - Image URL:', image);
  console.log('PackageHero - Title:', title);

  const handleImageError = () => {
    console.error('PackageHero - Image failed to load:', image);
    setImageError(true);
  };

  const handleImageLoad = () => {
    console.log('PackageHero - Image loaded successfully:', image);
    setImageLoaded(true);
  };

  // Fallback image from Supabase storage
  const fallbackImage = getTemporaryFallbackImage();
  const imageUrl = image || fallbackImage.url;

  return (
    <section className="relative h-[70vh] w-full overflow-hidden">
      <div className="h-full w-full">
        <LazyImage
          src={imageUrl}
          alt={title || 'Package hero image'}
          className="h-full w-full object-cover transition-transform duration-800 hover:scale-110"
          width={1920}
          height={700}
          priority={true}
          skeletonVariant="hero"
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      </div>
      <div className="absolute inset-0 bg-gradient-to-b from-black/50 to-black/70" />
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-white w-[90%] z-10">
        <h1 className="text-4xl font-bold mb-4 animate-fadeInDown">{title}</h1>
      </div>
    </section>
  );
};

export default PackageHero;
