/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @next/next/no-img-element */
import React, { useState, useEffect } from "react";
import { Upload, Eye, Calendar, Tag, User, Globe, Settings } from "lucide-react";

// Type definitions
interface FormData {
  title: string;
  description: string;
  imageUrl: string;
  imageAlt: string;
  category: string;
  status: 'published' | 'draft' | 'archived';
}

interface BlogCardAdminProps {
  formData?: any;
  onFormDataChange?: (data: any) => void;
}

const BlogCardAdmin: React.FC<BlogCardAdminProps> = ({ formData: externalFormData, onFormDataChange }) => {
  const [internalFormData, setInternalFormData] = useState<FormData>({
    title: "",
    description: "",
    imageUrl: "",
    imageAlt: "",
    category: "",
    status: "draft",
  });

  const [previewImage, setPreviewImage] = useState<string>("");
  const [showCustomCategory, setShowCustomCategory] = useState<boolean>(false);
  const [showPreview] = useState(false);

  // Use external formData if provided, otherwise use internal state
  const formData = externalFormData || internalFormData;

  // Initialize preview image with existing imageUrl from formData
  useEffect(() => {
    if (formData.imageUrl && !previewImage) {
      setPreviewImage(formData.imageUrl);
    }
  }, [formData.imageUrl, previewImage]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    const newData = { [name]: value };

    // Handle category selection
    if (name === 'category') {
      if (value === 'custom') {
        setShowCustomCategory(true);
        return;
      } else {
        setShowCustomCategory(false);
      }
    }

    if (onFormDataChange) {
      onFormDataChange(newData);
    } else {
      setInternalFormData((prev) => ({
        ...prev,
        ...newData,
      }));
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        const result = e.target?.result;
        if (typeof result === "string") {
          const imageUrl = result;
          setPreviewImage(imageUrl);

          if (onFormDataChange) {
            onFormDataChange({ imageUrl });
          } else {
            setInternalFormData((prev) => ({
              ...prev,
              imageUrl: imageUrl,
            }));
          }
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log("Blog card data:", formData);
    // Here you would typically send the data to your backend
    alert("Blog card saved successfully!");
  };

  const resetForm = () => {
    // Reset form logic here
  };

  return (
    <div className="min-h-screen bg-[var(--background)] p-4">
      <div className="max-w-5xl mx-auto">
        <h1 className="text-2xl font-bold text-[var(--text)] mb-6 text-center">
          Blog Card Admin Panel
        </h1>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* Upload Form Section */}
          <div className="bg-[var(--white)] rounded-lg shadow-lg p-4">
            <h2 className="text-lg font-semibold text-[var(--text)] mb-4 flex items-center gap-2">
              <Upload size={18} />
              Create Blog Card
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Card Title */}
              <div>
                <label className="block text-sm font-medium text-[var(--text)] mb-2">
                  Card Title *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="Enter blog card title"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent outline-none transition-all"
                />
              </div>

              {/* Card Description */}
              <div>
                <label className="block text-sm font-medium text-[var(--text)] mb-2">
                  Card Description *
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter blog card description"
                  required
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent outline-none transition-all resize-vertical"
                />
              </div>

              {/* Category Selection */}
              <div>
                <label className="block text-sm font-medium text-[var(--text)] mb-2">
                  Category *
                </label>
                <select
                  name="category"
                  value={showCustomCategory ? 'custom' : formData.category}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent outline-none transition-all"
                >
                  <option value="">Select a category</option>
                  <option value="Travel tips">Travel tips</option>
                  <option value="Inspiring stories">Inspiring stories</option>
                  <option value="Community">Community</option>
                  <option value="custom">Custom category</option>
                </select>

                {showCustomCategory && (
                  <input
                    type="text"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    placeholder="Enter custom category"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent outline-none transition-all mt-2"
                  />
                )}
              </div>

              {/* Status Selection */}
              <div>
                <label className="block text-sm font-medium text-[var(--text)] mb-2">
                  Status *
                </label>
                <select
                  name="status"
                  value={formData.status || 'draft'}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent outline-none transition-all"
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              {/* Image Upload */}
              <div>
                <label className="block text-sm font-medium text-[var(--text)] mb-2">
                  Card Image *
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-[var(--accent)] transition-colors">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <label
                    htmlFor="image-upload"
                    className="cursor-pointer flex flex-col items-center gap-2"
                  >
                    <Upload size={24} className="text-gray-400" />
                    <span className="text-sm text-gray-500">
                      Click to upload image or drag and drop
                    </span>
                    <span className="text-xs text-gray-400">
                      PNG, JPG, GIF up to 10MB
                    </span>
                  </label>

                  {previewImage && (
                    <div className="mt-4">
                      <img
                        src={previewImage}
                        alt="Preview"
                        className="max-w-full h-24 object-cover rounded-lg mx-auto"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Image Alt Text */}
              <div>
                <label className="block text-sm font-medium text-[var(--text)] mb-2">
                  Image Description (Alt Text) *
                </label>
                <input
                  type="text"
                  name="imageAlt"
                  value={formData.imageAlt}
                  onChange={handleInputChange}
                  placeholder="Describe the image for search engines and accessibility"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent outline-none transition-all"
                />
                <p className="text-xs text-gray-500 mt-1">
                  This helps with SEO and accessibility for screen readers
                </p>
              </div>
            </form>
          </div>

          {/* Preview Section */}
          <div className="bg-[var(--white)] rounded-lg shadow-lg p-4">
            <h2 className="text-lg font-semibold text-[var(--text)] mb-4 flex items-center gap-2">
              <Eye size={18} />
              Live Preview
            </h2>

            {showPreview ||
            formData.title ||
            formData.description ||
            previewImage ? (
              <div className="bg-[var(--card-bg)] rounded-lg overflow-hidden shadow-md border border-gray-200 hover:shadow-lg transition-shadow">
                {/* Card Image */}
                {previewImage ? (
                  <div className="aspect-video overflow-hidden">
                    <img
                      src={previewImage}
                      alt={formData.imageAlt || "Blog card image"}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                ) : (
                  <div className="aspect-video bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400">No image uploaded</span>
                  </div>
                )}

                {/* Card Content */}
                <div className="p-4">
                  {formData.category && (
                    <span className="inline-block bg-[var(--accent)] text-white px-2 py-1 rounded-full text-xs font-medium mb-2">
                      {formData.category}
                    </span>
                  )}

                  <h3 className="text-lg font-bold text-[var(--text)] mb-2 line-clamp-2">
                    {formData.title || "Your blog title will appear here"}
                  </h3>

                  <p className="text-gray-600 mb-3 line-clamp-3 text-sm">
                    {formData.description ||
                      "Your blog description will appear here. This is where you can provide a brief summary of your blog post content."}
                  </p>

                  <button className="bg-[var(--accent)] text-white px-3 py-1.5 rounded-lg font-medium hover:opacity-90 transition-opacity text-sm">
                    Read More
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Eye size={36} className="mx-auto mb-3 opacity-50" />
                <p className="text-sm">
                  Fill out the form to see your blog card preview
                </p>
              </div>
            )}

            {/* SEO Info */}
            {formData.imageAlt && (
              <div className="mt-4 p-3 bg-[var(--hero)] rounded-lg">
                <h4 className="font-medium text-[var(--text)] mb-1 text-sm">
                  SEO Information
                </h4>
                <p className="text-xs text-gray-600">
                  <strong>Image Alt Text:</strong> {formData.imageAlt}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogCardAdmin;
