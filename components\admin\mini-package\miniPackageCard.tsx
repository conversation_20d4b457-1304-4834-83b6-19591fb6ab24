/* eslint-disable @next/next/no-img-element */
import React, { useState, ChangeEvent } from 'react';
import { Upload, MapPin, Star, TreePine, Mountain, Building, Waves } from 'lucide-react';
import Image from 'next/image';

interface MiniPackageFormData {
  title: string;
  location: string;
  difficulty: string;
  content: unknown[];
  duration: string;
  pricing: {
    solo: number;
    honeymoon: number;
    family: number;
    group: number;
  };
  category: string;
  status: 'published' | 'draft' | 'archived';

  imageUrl: string;
  imageAlt: string;
  slug: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  ogTitle: string;
  ogDescription: string;
  ogImageUrl: string;
  canonicalUrl: string;
  robotsIndex: string;
  robotsFollow: string;
  highlights: string[];
  packingList: string[];
  includes: string[];
  excludes: string[];
}

interface MiniPackageCardProps {
  formData: MiniPackageFormData;
  onFormDataChange: (data: Partial<MiniPackageFormData>) => void;
}

// Validation function to check if form data contains blob URLs
export const validateMiniPackageFormData = (formData: MiniPackageFormData): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (formData.imageUrl && formData.imageUrl.startsWith('blob:')) {
    errors.push('Main image contains invalid URL. Please upload the image properly.');
  }

  if (!formData.title.trim()) {
    errors.push('Title is required.');
  }

  if (!formData.location.trim()) {
    errors.push('Location is required.');
  }

  if (!formData.duration.trim()) {
    errors.push('Duration is required.');
  }

  if (!formData.imageUrl.trim()) {
    errors.push('Main image is required.');
  }

  if (!formData.imageAlt.trim()) {
    errors.push('Image alt text is required.');
  }

  // Check if at least one pricing option is set
  const hasPricing = Object.values(formData.pricing).some(price => price > 0);
  if (!hasPricing) {
    errors.push('At least one pricing option must be set.');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export default function MiniPackageCardAdmin({ formData, onFormDataChange }: MiniPackageCardProps) {
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const updateFormData = (updates: Partial<MiniPackageFormData>) => {
    console.log('MiniPackageCard - Updating form data with:', updates);
    onFormDataChange(updates);
  };

  const handleImageUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    try {
      // Create preview immediately
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Upload to server
      const uploadFormData = new FormData();
      uploadFormData.append('file', file);
      uploadFormData.append('altText', formData.imageAlt || file.name.split('.')[0]);
      uploadFormData.append('bucket', 'sas-mini-package-images');

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: uploadFormData
      });

      const result = await response.json();

      console.log('MiniPackageCard - Upload result:', result);

      if (result.success) {
        // Validate that we got a proper Supabase URL, not a blob URL
        if (result.data.url && result.data.url.startsWith('blob:')) {
          console.error('MiniPackageCard - Received blob URL from upload API:', result.data.url);
          alert('Upload failed: Invalid image URL received. Please try again.');
          return;
        }

        // Validate that we got a Supabase storage URL
        if (!result.data.url || !result.data.url.includes('supabase.co/storage')) {
          console.error('MiniPackageCard - Invalid URL from upload API:', result.data.url);
          alert('Upload failed: Invalid image URL. Please try again.');
          return;
        }

        console.log('MiniPackageCard - Updating form data with:', {
          imageUrl: result.data.url,
          imageAlt: result.data.altText
        });

        // Update form data with uploaded image URL
        const updateData = {
          imageUrl: result.data.url,
          imageAlt: result.data.altText
        };

        console.log('MiniPackageCard - About to update form data with:', updateData);
        updateFormData(updateData);

        // Clear the blob URL preview so it uses the Supabase URL from formData
        setImagePreview(null);

        setTimeout(() => {
          console.log('MiniPackageCard - Form data after update (delayed):', formData);
        }, 100);
      } else {
        console.error('Failed to upload image:', result.error);
        alert('Failed to upload image: ' + result.error);
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
    }
  };

  const getDifficultyColor = (difficulty: string): React.CSSProperties => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return { color: 'var(--light-green)' };
      case 'moderate': return { color: 'var(--accent)' };
      case 'hard': return { color: '#dc2626' }; // red-600 equivalent
      default: return { color: 'var(--text)' };
    }
  };

  const getCategoryIcon = (category: string): React.ReactNode => {
    switch (category.toLowerCase()) {
      case 'wildlife': return <TreePine className="w-4 h-4" />;
      case 'adventure': return <Mountain className="w-4 h-4" />;
      case 'cultural': return <Building className="w-4 h-4" />;
      case 'beach': return <Waves className="w-4 h-4" />;
      default: return <Star className="w-4 h-4" />;
    }
  };

  const getLowestPrice = () => {
    const prices = Object.values(formData.pricing).filter(price => price > 0);
    return prices.length > 0 ? Math.min(...prices) : 0;
  };

  const displayImageUrl = imagePreview || formData.imageUrl;

  return (
    <div className="p-6 bg-white">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Form Section */}
        <div className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
            
            {/* Title */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => updateFormData({ title: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter mini package title"
              />
            </div>

            {/* Location */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Location *
              </label>
              <select
                value={formData.location}
                onChange={(e) => updateFormData({ location: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Location</option>
                <option value="Rwanda">Rwanda</option>
                <option value="Tanzania">Tanzania</option>
                <option value="Uganda">Uganda</option>
                <option value="South Africa">South Africa</option>
                <option value="Rwanda - Luxury">Rwanda - Luxury</option>
                <option value="Tanzania - Luxury">Tanzania - Luxury</option>
                <option value="Uganda - Luxury">Uganda - Luxury</option>
                <option value="South Africa - Luxury">South Africa - Luxury</option>
              </select>
            </div>

            {/* Duration */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Duration *
              </label>
              <input
                type="text"
                value={formData.duration}
                onChange={(e) => updateFormData({ duration: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., 3 Days 2 Nights"
              />
            </div>

            {/* Category */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                value={formData.category}
                onChange={(e) => updateFormData({ category: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Wildlife">Wildlife</option>
                <option value="Adventure">Adventure</option>
                <option value="Cultural">Cultural</option>
                <option value="Beach">Beach</option>
                <option value="Luxury">Luxury</option>
                <option value="Budget">Budget</option>
              </select>
            </div>

            {/* Difficulty */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Difficulty *
              </label>
              <select
                value={formData.difficulty}
                onChange={(e) => updateFormData({ difficulty: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Easy">Easy</option>
                <option value="Moderate">Moderate</option>
                <option value="Hard">Hard</option>
              </select>
            </div>

            {/* Pricing */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pricing (USD) *
              </label>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Solo</label>
                  <input
                    type="number"
                    value={formData.pricing.solo}
                    onChange={(e) => updateFormData({ 
                      pricing: { ...formData.pricing, solo: Number(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Honeymoon</label>
                  <input
                    type="number"
                    value={formData.pricing.honeymoon}
                    onChange={(e) => updateFormData({ 
                      pricing: { ...formData.pricing, honeymoon: Number(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Family</label>
                  <input
                    type="number"
                    value={formData.pricing.family}
                    onChange={(e) => updateFormData({ 
                      pricing: { ...formData.pricing, family: Number(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">Group</label>
                  <input
                    type="number"
                    value={formData.pricing.group}
                    onChange={(e) => updateFormData({ 
                      pricing: { ...formData.pricing, group: Number(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0"
                  />
                </div>
              </div>
            </div>

            {/* Image Upload */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Main Image *
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label htmlFor="image-upload" className="cursor-pointer">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-600">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                </label>
              </div>
            </div>

            {/* Image Alt Text */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Image Alt Text *
              </label>
              <input
                type="text"
                value={formData.imageAlt}
                onChange={(e) => updateFormData({ imageAlt: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Describe the image for accessibility"
              />
            </div>
          </div>
        </div>

        {/* Preview Section */}
        <div className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Live Preview</h2>
            
            {/* Mini Package Card Preview */}
            <div className="bg-[var(--secondary-background)] shadow-lg overflow-hidden max-w-sm mx-auto">
              {/* Header Image Area */}
              <div className="relative h-48 bg-gray-200">
                {displayImageUrl ? (
                  <Image
                    src={displayImageUrl}
                    alt={formData.imageAlt || "Mini package preview"}
                    className="w-full h-full object-cover"
                    width={400}
                    height={192}
                    style={{objectFit: 'cover'}}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    <Upload className="w-12 h-12" />
                  </div>
                )}
                
                {/* Price Badge */}
                {getLowestPrice() > 0 && (
                  <div className="absolute top-4 right-4 bg-white rounded-lg px-3 py-2 shadow-md">
                    <div className="text-sm font-medium text-gray-900">From ${getLowestPrice()}</div>
                  </div>
                )}

                {/* Location Overlay at Bottom */}
                {formData.location && (
                  <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white px-4 py-3">
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-orange-500" />
                      <span className="font-medium">{formData.location}</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Content Area */}
              <div className="bg-[var(--secondary-background)] p-6">
                {/* Title */}
                <h2 className="text-xl font-bold text-gray-900 mb-3">
                  {formData.title || 'Mini Package Title'}
                </h2>

                {/* Duration */}
                <div className="text-gray-700 text-lg mb-6">
                  {formData.duration || 'Duration'}
                </div>

                {/* View Details Button */}
                <div className="flex justify-center">
                  <button className="btn text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200">
                    View Details
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
