{"name": "sas-website-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "verify": "node scripts/verify-setup.js", "setup": "npm install && npm run verify", "setup-supabase": "node scripts/setup-supabase.js"}, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@supabase/supabase-js": "^2.50.3", "@tailwindcss/typography": "^0.5.16", "@tinymce/tinymce-react": "^6.2.1", "@types/nodemailer": "^6.4.17", "date-fns": "^4.1.0", "dotenv": "^17.0.1", "isomorphic-dompurify": "^2.26.0", "lucide-react": "^0.522.0", "lz-string": "^1.5.0", "next": "15.3.4", "nodemailer": "^7.0.5", "react": "^19.0.0", "react-burger-menu": "^3.1.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-pdf": "^10.0.1", "react-responsive": "^10.0.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "stripe": "^18.2.1", "tinymce": "^7.9.1", "web-vitals": "^5.0.3", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}