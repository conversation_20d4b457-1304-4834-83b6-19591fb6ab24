'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Package, Save, Upload } from 'lucide-react';
import MiniPackageCardAdmin, { validateMiniPackageFormData } from '@/components/admin/mini-package/miniPackageCard';
import MiniPackageEditor from '@/components/admin/mini-package/miniPackageEditor';
import MiniPackageSEOForm from '@/components/admin/mini-package/miniPackageSeo';
import MiniPackageItinerary from '@/components/admin/mini-package/miniPackageItinerary';
import MiniPackagePacking from '@/components/admin/mini-package/miniPackagePacking';

interface ContentBlock {
  id: string;
  type: string;
  content: string | object | string[];
  imageUrl?: string;
  imageAlt?: string;
  imageCaption?: string;
}

interface ItineraryDay {
  id: number;
  hour: number;
  title: string;
  description: string;
}

interface MiniPackageFormData {
  title: string;
  location: string;
  difficulty: string;
  content: ContentBlock[];
  duration: string;
  pricing: {
    solo: number;
    honeymoon: number;
    family: number;
    group: number;
  };
  category: string;
  status: 'published' | 'draft' | 'archived';

  imageUrl: string;
  imageAlt: string;
  slug: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  ogTitle: string;
  ogDescription: string;
  ogImageUrl: string;
  canonicalUrl: string;
  robotsIndex: string;
  robotsFollow: string;
  highlights: string[];
  packingList: string[];
  includes: string[];
  excludes: string[];
  itinerary: ItineraryDay[];
  images: Array<{
    url: string;
    alt: string;
    caption?: string;
  }>;
}

const styles = `
  ::-webkit-scrollbar {
    width: 8px;
  }
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function AddMiniPackagePage() {
  const router = useRouter();
  
  const [activeTab, setActiveTab] = useState('basic');
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<MiniPackageFormData>({
    title: '',
    location: '',
    difficulty: 'Moderate',
    content: [],
    duration: '',
    pricing: {
      solo: 0,
      honeymoon: 0,
      family: 0,
      group: 0
    },
    category: 'Wildlife',
    status: 'draft',

    imageUrl: '',
    imageAlt: '',
    slug: '',
    seoTitle: '',
    seoDescription: '',
    seoKeywords: [],
    ogTitle: '',
    ogDescription: '',
    ogImageUrl: '',
    canonicalUrl: '',
    robotsIndex: 'index',
    robotsFollow: 'follow',
    highlights: [],
    packingList: [],
    includes: [],
    excludes: [],
    itinerary: [],
    images: []
  });

  const tabs = [
    { id: 'basic', label: 'Basic Info', component: 'MiniPackageCard' },
    { id: 'content', label: 'Content', component: 'MiniPackageEditor' },
    { id: 'itinerary', label: 'Itinerary', component: 'MiniPackageItinerary' },
    { id: 'packing', label: 'Packing List', component: 'MiniPackagePacking' },
    { id: 'seo', label: 'SEO', component: 'MiniPackageSeo' }
  ];

  const handleSave = async (status: 'draft' | 'active' = 'draft') => {
    setLoading(true);
    try {
      // Validate form data before submission
      const validation = validateMiniPackageFormData(formData);
      if (!validation.isValid) {
        alert('Please fix the following errors:\n' + validation.errors.join('\n'));
        setLoading(false);
        return;
      }

      // Transform content blocks for API
      const contentBlocks = formData.content.map((block, index) => ({
        block_type: block.type,
        content: typeof block.content === 'string' ? block.content : null,
        content_data: typeof block.content === 'object' ? block.content : null,
        image_url: block.imageUrl || null,
        image_alt: block.imageAlt || null,
        image_caption: block.imageCaption || null,
        sort_order: index
      }));

      // Transform itinerary for API
      const itinerary = formData.itinerary.map((hour, index) => ({
        hour_number: hour.hour,
        title: hour.title,
        description: hour.description,
        sort_order: index
      }));

      // Transform images for API
      const images = formData.images.map((image, index) => ({
        image_url: image.url,
        image_alt: image.alt,
        caption: image.caption || null,
        sort_order: index,
        is_featured: false
      }));

      const miniPackageData = {
        title: formData.title,
        location: formData.location,
        difficulty: formData.difficulty,
        category: formData.category,
        duration: formData.duration,
        pricing: formData.pricing,
        status,

        imageUrl: formData.imageUrl,
        imageAlt: formData.imageAlt,
        heroImageUrl: formData.imageUrl, // Use main image as hero
        heroImageAlt: formData.imageAlt,
        seoTitle: formData.seoTitle,
        seoDescription: formData.seoDescription,
        seoKeywords: formData.seoKeywords,
        ogTitle: formData.ogTitle,
        ogDescription: formData.ogDescription,
        ogImageUrl: formData.ogImageUrl,
        canonicalUrl: formData.canonicalUrl,
        robotsIndex: formData.robotsIndex,
        robotsFollow: formData.robotsFollow,
        highlights: formData.highlights,
        packingList: formData.packingList,
        includes: formData.includes,
        excludes: formData.excludes,
        content: contentBlocks,
        itinerary: itinerary,
        images: images
      };

      console.log('Mini Package Add Form - Sending to API:', {
        imageUrl: miniPackageData.imageUrl,
        heroImageUrl: miniPackageData.heroImageUrl,
        imageAlt: miniPackageData.imageAlt,
        heroImageAlt: miniPackageData.heroImageAlt
      });

      const response = await fetch('/api/admin/mini-packages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(miniPackageData)
      });

      const result = await response.json();

      if (result.success) {
        // Redirect to mini packages list
        router.push('/admin/mini-packages');
      } else {
        console.error('Failed to save mini package:', result.error);
        alert('Failed to save mini package. Please try again.');
      }
    } catch (error) {
      console.error('Error saving mini package:', error);
      alert('Failed to save mini package. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePublish = () => {
    handleSave('active');
  };

  const handleFormDataChange = (newData: Partial<MiniPackageFormData>) => {
    console.log('Mini Package Add Form - Form data change:', newData);
    setFormData(prev => {
      const updated = { ...prev, ...newData };
      console.log('Mini Package Add Form - Updated form data:', {
        imageUrl: updated.imageUrl,
        imageAlt: updated.imageAlt
      });
      return updated;
    });
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'basic':
        return (
          <MiniPackageCardAdmin
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
      case 'content':
        return (
          <MiniPackageEditor
            initialContent={formData.content}
            onSave={(content) => handleFormDataChange({ content })}
          />
        );
      case 'itinerary':
        return (
          <MiniPackageItinerary
            initialItinerary={formData.itinerary}
            onSave={(itinerary) => handleFormDataChange({ itinerary })}
          />
        );
      case 'packing':
        return (
          <MiniPackagePacking
            highlights={formData.highlights}
            packingList={formData.packingList}
            includes={formData.includes}
            excludes={formData.excludes}
            onSave={(data) => handleFormDataChange(data)}
          />
        );
      case 'seo':
        return (
          <MiniPackageSEOForm
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
      default:
        return (
          <MiniPackageCardAdmin
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
    }
  };

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300 bg-white rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <Package className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Add New Mini Package</h1>
                <p className="text-gray-600">Create a new mini travel package</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={() => handleSave('draft')}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <Save className="w-4 h-4" />
                {loading ? 'Saving...' : 'Save Draft'}
              </button>
              <button
                onClick={handlePublish}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                <Upload className="w-4 h-4" />
                {loading ? 'Publishing...' : 'Publish'}
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white border-b border-gray-200">
          <div className="px-6">
            <nav className="flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto" style={{ scrollbarWidth: 'thin', scrollbarColor: '#6B7280 transparent' }}>
          {renderTabContent()}
        </div>
      </div>
    </main>
  );
}
