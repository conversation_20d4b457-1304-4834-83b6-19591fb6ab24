"use client";

import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { ArrowLeft, BookOpen, Save, Trash2 } from "lucide-react";
import BlogCard from "@/components/admin/blog/blogCard";
import BlogEditor from "@/components/admin/blog/editor";
import BlogSEO from "@/components/admin/blog/seo";

interface ImageContent {
  src: string;
  alt: string;
  caption: string;
}

interface VideoContent {
  src: string;
  poster?: string;
  caption: string;
  width?: 'sm' | 'md' | 'lg' | 'full';
}

interface QuoteContent {
  content: string;
  author: string;
  source: string;
}

interface ContentBlock {
  id: number;
  type: string;
  content: string | string[] | ImageContent | VideoContent | QuoteContent | null;
}

interface BlogFormData {
  title: string;
  description: string;
  imageUrl: string;
  imageAlt: string;
  category: string;
  tags: string[];
  status: "published" | "draft" | "archived";
  content: ContentBlock[];
  publishedAt: string;
  slug: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  ogTitle: string;
  ogDescription: string;
  ogImageUrl: string;
  canonicalUrl: string;
  robotsIndex: string;
  robotsFollow: string;
  schemaData: any;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function EditBlogPage() {
  const params = useParams();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("basic");
  const [loading, setLoading] = useState(false);
  const [blogLoading, setBlogLoading] = useState(true);
  const [formData, setFormData] = useState<BlogFormData | null>(null);

  const tabs = [
    { id: "basic", label: "Basic Info", component: "BlogCard" },
    { id: "content", label: "Content", component: "BlogEditor" },
    { id: "seo", label: "SEO", component: "BlogSEO" },
  ];

  // Load blog data
  useEffect(() => {
    const loadBlogData = async () => {
      try {
        // Fetch blog post data from API
        const response = await fetch(`/api/admin/blog/${params.slug}`);
        const data = await response.json();

        if (data.success) {
          const post = data.data;


          // Convert content blocks to proper format for editor
          let contentBlocks: ContentBlock[] = [];
          if (post.content && Array.isArray(post.content)) {

            contentBlocks = post.content.map((block: any, index: number) => {
              // Convert database block format to editor format
              let editorContent: string | string[] | ImageContent | VideoContent | QuoteContent | null = '';
              let editorType = block.block_type || 'paragraph';

              // Map database block types to editor block types
              const typeMapping: { [key: string]: string } = {
                'h2': 'heading2',
                'h3': 'heading3',
                'h4': 'heading4',
                'h5': 'heading5',
                'h6': 'heading6',
                'listing': 'bulleted-list',
                'paragraph': 'paragraph',
                'image': 'image',
                'video': 'video',
                'quote': 'quote'
              };

              editorType = typeMapping[block.block_type] || 'paragraph';

              if (block.block_type === 'image' && block.content) {
                // Convert image block
                editorContent = {
                  src: block.content.src || '',
                  alt: block.content.alt || '',
                  caption: block.content.caption || ''
                };
              } else if (block.block_type === 'listing' && block.content) {
                // Convert list block
                editorContent = block.content.items || [];
              } else if (block.block_type === 'video' && block.content) {
                // Convert video block
                editorContent = {
                  src: block.content.src || '',
                  poster: block.content.poster || '',
                  caption: block.content.caption || '',
                  width: block.content.width || 'lg'
                } as VideoContent;
              } else if (block.block_type === 'quote' && block.content) {
                // Convert quote block - ensure it's in object format
                if (typeof block.content === 'object' && block.content && 'content' in block.content) {
                  editorContent = {
                    content: (block.content as QuoteContent).content || '',
                    author: (block.content as QuoteContent).author || '',
                    source: (block.content as QuoteContent).source || ''
                  } as QuoteContent;
                } else if (typeof block.content === 'string') {
                  editorContent = {
                    content: block.content,
                    author: '',
                    source: ''
                  } as QuoteContent;
                }
              } else if (block.content && typeof block.content === 'object' && block.content.content) {
                // Convert text blocks (paragraph, headings, etc.)
                editorContent = block.content.content;
              } else if (typeof block.content === 'string') {
                editorContent = block.content;
              }

              const convertedBlock = {
                id: index + 1,
                type: editorType,
                content: editorContent
              };

              return convertedBlock;
            });
          }

          const blogData: BlogFormData = {
            title: post.title || "",
            description: post.description || "",
            imageUrl: post.hero_image_url || "",
            imageAlt: post.hero_image_alt || "",
            category: post.category || "",
            tags: post.tags || [],
            status: post.status || "draft",
            content: contentBlocks,
            publishedAt: post.published_at || "",
            slug: post.slug || "",
            seoTitle: post.seo_title || "",
            seoDescription: post.seo_description || "",
            seoKeywords: post.seo_keywords || [],
            ogTitle: post.og_title || "",
            ogDescription: post.og_description || "",
            ogImageUrl: post.og_image_url || "",
            canonicalUrl: post.canonical_url || "",
            robotsIndex: post.robots_index || "index",
            robotsFollow: post.robots_follow || "follow",
            schemaData: post.schema_data || null,
          };
          setFormData(blogData);
        } else {
          throw new Error(data.error || "Failed to load blog post");
        }
      } catch (error) {
        console.error("Error loading blog data:", error);
        alert("Error loading blog data. Please try again.");
      } finally {
        setBlogLoading(false);
      }
    };

    if (params.slug) {
      loadBlogData();
    }
  }, [params.slug]);

  const handleFormDataChange = (data: Partial<BlogFormData>) => {
    if (formData) {
      setFormData((prev) => ({ ...prev!, ...data }));
    }
  };

  const handleSave = async (statusOverride?: string) => {
    if (!formData) return;

    setLoading(true);
    try {
      // Validate required fields
      if (
        !formData.title ||
        !formData.description ||
        !formData.category
      ) {
        alert(
          "Please fill in all required fields (Title, Description, Category)"
        );
        setLoading(false);
        return;
      }

      // Prepare data for API
      // Convert content blocks back to database format
      const contentForAPI = formData.content.map((block, index) => {
        let dbContent: any;

        if (block.type === 'image' && typeof block.content === 'object' && block.content !== null && 'src' in block.content && 'alt' in block.content) {
          // Convert image block back to database format
          dbContent = {
            src: block.content.src,
            alt: block.content.alt,
            caption: block.content.caption,
            width: 'full' // default width
          };
        } else if (block.type === 'video' && typeof block.content === 'object' && block.content !== null && 'src' in block.content && 'poster' in block.content) {
          // Convert video block back to database format
          const videoContent = block.content as VideoContent;
          dbContent = {
            src: videoContent.src,
            poster: videoContent.poster || '',
            caption: videoContent.caption,
            width: videoContent.width || 'lg'
          };
        } else if (block.type === 'listing' && Array.isArray(block.content)) {
          // Convert list block back to database format
          dbContent = {
            items: block.content,
            listType: 'unordered' // default list type
          };
        } else {
          // Convert text blocks back to database format
          dbContent = {
            content: block.content
          };
        }

        return {
          block_type: block.type,
          content: dbContent,
          sort_order: index
        };
      });

      const blogData = {
        title: formData.title,
        slug: formData.slug,
        description: formData.description,
        hero_image_url: formData.imageUrl,
        hero_image_alt: formData.imageAlt,
        category: formData.category,
        tags: formData.tags,
        status: statusOverride || formData.status,
        content: contentForAPI,
        seo_title: formData.seoTitle,
        seo_description: formData.seoDescription,
        seo_keywords: formData.seoKeywords,
        og_title: formData.ogTitle,
        og_description: formData.ogDescription,
        og_image_url: formData.ogImageUrl,
        canonical_url: formData.canonicalUrl,
        robots_index: formData.robotsIndex,
        robots_follow: formData.robotsFollow,
        schema_data: formData.schemaData,
      };

      // Make API call to update blog post
      const response = await fetch(`/api/admin/blog/${params.slug}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(blogData),
      });

      const data = await response.json();

      if (data.success) {
        alert("Blog post updated successfully!");
        router.push("/admin/blog");
      } else {
        throw new Error(data.error || "Failed to update blog post");
      }
    } catch (error) {
      console.error("Error updating blog post:", error);
      alert("Error updating blog post. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    const confirmed = window.confirm(
      "Are you sure you want to delete this blog post? This action cannot be undone."
    );
    if (!confirmed) return;

    setLoading(true);
    try {
      // Here you would typically make an API call to delete the blog post
      console.log("Deleting blog post:", params.slug);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      alert("Blog post deleted successfully!");
      router.push("/admin/blog");
    } catch (error) {
      console.error("Error deleting blog post:", error);
      alert("Error deleting blog post. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const renderTabContent = () => {
    if (!formData) return null;

    switch (activeTab) {
      case "basic":
        return (
          <BlogCard
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
      case "content":

        return (
          <BlogEditor
            initialContent={formData.content}
            onSave={(content) => handleFormDataChange({ content })}
          />
        );
      case "seo":
        return (
          <BlogSEO
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
      default:
        return null;
    }
  };

  if (blogLoading) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--accent)] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading blog post...</p>
        </div>
      </div>
    );
  }

  if (!formData) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Blog post not found
          </h2>
          <p className="text-gray-600 mb-4">
            The blog post you're looking for doesn't exist.
          </p>
          <button
            onClick={() => router.push("/admin/blog")}
            className="px-4 py-2 bg-[var(--accent)] text-white rounded-lg hover:bg-[var(--accent)]/90"
          >
            Back to Blog Management
          </button>
        </div>
      </div>
    );
  }

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[var(--accent)] rounded-lg">
                  <BookOpen className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-[var(--text)]">
                    Edit Blog Post
                  </h1>
                  <p className="text-gray-600">
                    Update your blog post content and settings
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={handleDelete}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors disabled:opacity-50"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </button>
              <button
                onClick={() => handleSave("draft")}
                disabled={loading}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                Save as Draft
              </button>
              <button
                onClick={() => handleSave("published")}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                Publish
              </button>
              <button
                onClick={() => handleSave()}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 bg-[var(--accent)] text-white rounded-lg hover:bg-[var(--accent)]/90 transition-colors disabled:opacity-50"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {loading ? "Saving..." : `Update as ${formData.status === 'published' ? 'Published' : formData.status === 'draft' ? 'Draft' : 'Archived'}`}
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white border-b border-gray-200">
          <div className="px-6">
            <nav className="flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? "border-[var(--accent)] text-[var(--accent)]"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-6">
          <div className="max-w-7xl mx-auto">{renderTabContent()}</div>
        </div>
      </div>
    </main>
  );
}
