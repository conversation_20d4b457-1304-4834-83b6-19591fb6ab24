import { NextRequest } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Check if user is authenticated admin
export async function verifyAdminAuth(request: NextRequest): Promise<{
  isAuthenticated: boolean;
  isAdmin: boolean;
  user?: any;
  error?: string;
}> {
  try {
    // In development mode, bypass authentication
    if (process.env.NODE_ENV === 'development') {
      return {
        isAuthenticated: true,
        isAdmin: true,
        user: { id: 'dev-user', email: '<EMAIL>', role: 'admin' }
      };
    }

    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        error: 'No authorization token provided'
      };
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Create Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Verify token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        error: 'Invalid or expired token'
      };
    }

    // Check if user is admin
    const { data: userProfile, error: profileError } = await supabase
      .from('sas_users')
      .select('role, status')
      .eq('id', user.id)
      .single();

    if (profileError || !userProfile) {
      return {
        isAuthenticated: true,
        isAdmin: false,
        error: 'User profile not found'
      };
    }

    if (userProfile.status !== 'active') {
      return {
        isAuthenticated: true,
        isAdmin: false,
        error: 'User account is not active'
      };
    }

    const isAdmin = userProfile.role === 'admin';

    return {
      isAuthenticated: true,
      isAdmin,
      user: {
        ...user,
        role: userProfile.role,
        status: userProfile.status
      }
    };

  } catch (error) {
    console.error('Admin auth verification error:', error);
    return {
      isAuthenticated: false,
      isAdmin: false,
      error: 'Authentication verification failed'
    };
  }
}

// Middleware wrapper for admin routes
export function withAdminAuth(handler: (request: NextRequest, context: any) => Promise<Response>) {
  return async (request: NextRequest, context: any) => {
    const auth = await verifyAdminAuth(request);

    if (!auth.isAuthenticated) {
      return new Response(
        JSON.stringify({ success: false, error: 'Authentication required' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    if (!auth.isAdmin) {
      return new Response(
        JSON.stringify({ success: false, error: 'Admin privileges required' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Add user info to request context
    (request as any).user = auth.user;

    return handler(request, context);
  };
}

// Simple session-based auth check for development
export function checkDevAuth(request: NextRequest): boolean {
  if (process.env.NODE_ENV === 'development') {
    return true; // Allow all requests in development
  }

  // In production, implement proper session checking
  const sessionCookie = request.cookies.get('admin-session');
  return !!sessionCookie;
}

// Generate secure admin session token
export function generateAdminSession(userId: string): string {
  const crypto = require('crypto');
  const secret = process.env.ADMIN_SESSION_SECRET || 'swift-africa-safaris-admin-2024';
  
  const payload = {
    userId,
    timestamp: Date.now(),
    role: 'admin'
  };

  return crypto
    .createHmac('sha256', secret)
    .update(JSON.stringify(payload))
    .digest('hex');
}

// Verify admin session token
export function verifyAdminSession(token: string): { valid: boolean; userId?: string } {
  try {
    // In a production environment, you would store and verify sessions in the database
    // For now, we'll use a simple validation
    if (!token || token.length !== 64) {
      return { valid: false };
    }

    // This is a simplified validation - in production, implement proper session management
    return { valid: true, userId: 'admin-user' };
  } catch (error) {
    return { valid: false };
  }
}

// Rate limiting for admin actions
const adminActionLimits = new Map<string, { count: number; resetTime: number }>();

export function checkAdminRateLimit(
  userId: string,
  action: string,
  maxRequests: number = 100,
  windowMs: number = 60 * 60 * 1000 // 1 hour
): { allowed: boolean; resetTime: number } {
  const now = Date.now();
  const key = `admin_${userId}_${action}`;
  
  const current = adminActionLimits.get(key);
  
  if (!current || now > current.resetTime) {
    adminActionLimits.set(key, {
      count: 1,
      resetTime: now + windowMs
    });
    return { allowed: true, resetTime: now + windowMs };
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, resetTime: current.resetTime };
  }
  
  adminActionLimits.set(key, {
    count: current.count + 1,
    resetTime: current.resetTime
  });
  
  return { allowed: true, resetTime: current.resetTime };
}

// Clean up expired admin rate limit entries
export function cleanupAdminRateLimit(): void {
  const now = Date.now();
  for (const [key, value] of adminActionLimits.entries()) {
    if (now > value.resetTime) {
      adminActionLimits.delete(key);
    }
  }
}

// Log admin actions for security audit
export async function logAdminAction(
  userId: string,
  action: string,
  details: any,
  ipAddress: string
): Promise<void> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    await supabase
      .from('sas_admin_logs')
      .insert({
        user_id: userId,
        action,
        details: JSON.stringify(details),
        ip_address: ipAddress,
        timestamp: new Date().toISOString()
      });
  } catch (error) {
    console.error('Failed to log admin action:', error);
  }
}
